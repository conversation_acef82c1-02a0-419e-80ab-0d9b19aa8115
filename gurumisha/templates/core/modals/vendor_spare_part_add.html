<!-- Vendor Add Spare Part Modal -->
<div class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50 p-4"
     x-data="{ show: true }"
     x-show="show"
     x-transition:enter="transition ease-out duration-300"
     x-transition:enter-start="opacity-0"
     x-transition:enter-end="opacity-100"
     x-transition:leave="transition ease-in duration-200"
     x-transition:leave-start="opacity-100"
     x-transition:leave-end="opacity-0"
     @click.self="show = false; setTimeout(() => $el.remove(), 200)">
    
    <div class="bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden"
         x-show="show"
         x-transition:enter="transition ease-out duration-300 transform"
         x-transition:enter-start="opacity-0 scale-95"
         x-transition:enter-end="opacity-100 scale-100"
         x-transition:leave="transition ease-in duration-200 transform"
         x-transition:leave-start="opacity-100 scale-100"
         x-transition:leave-end="opacity-0 scale-95">
        
        <!-- Modal Header -->
        <div class="bg-gradient-to-r from-harrier-red to-harrier-red-dark px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                        <i class="fas fa-plus text-white text-lg"></i>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold text-white font-montserrat">Add New Spare Part</h3>
                        <p class="text-white text-opacity-80 text-sm font-raleway">Add a new spare part to your inventory</p>
                    </div>
                </div>
                <button type="button"
                        class="text-white hover:text-gray-200 transition-colors p-2 hover:bg-white hover:bg-opacity-10 rounded-lg"
                        @click="show = false; setTimeout(() => $el.closest('.fixed').remove(), 200)">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
        </div>
        
        <!-- Modal Body -->
        <div class="bg-gradient-to-br from-gray-50 to-white px-6 py-6 max-h-[70vh] overflow-y-auto modal-body">
            <!-- Error Alert Container -->
            <div id="form-errors" class="hidden mb-6">
                <div class="bg-red-50 border border-red-200 rounded-xl p-4">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-triangle text-red-400 text-lg"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-bold text-red-800 font-montserrat">Please correct the following errors:</h3>
                            <div id="error-list" class="mt-2 text-sm text-red-700 font-raleway">
                                <!-- Errors will be populated here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Success Alert Container -->
            <div id="form-success" class="hidden mb-6">
                <div class="bg-green-50 border border-green-200 rounded-xl p-4">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <i class="fas fa-check-circle text-green-400 text-lg"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-bold text-green-800 font-montserrat">Success!</h3>
                            <p id="success-message" class="mt-1 text-sm text-green-700 font-raleway">
                                <!-- Success message will be populated here -->
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <form id="add-spare-part-form"
                  method="post"
                  enctype="multipart/form-data"
                  hx-post="{% url 'core:vendor_spare_part_add' %}"
                  hx-on::before-request="return handleFormSubmit()"
                  hx-on::after-request="handleFormResponse(event)"
                  class="space-y-6"
                  x-data="{ isSubmitting: false }"
                  @submit="isSubmitting = true"
                  novalidate>
                {% csrf_token %}
                
                <!-- Basic Information Section -->
                <div class="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
                    <h4 class="text-lg font-bold text-harrier-dark mb-4 font-montserrat flex items-center">
                        <i class="fas fa-info-circle mr-2 text-harrier-red"></i>Basic Information
                    </h4>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Part Name -->
                        <div class="space-y-2">
                            <label class="block text-sm font-bold text-harrier-dark font-montserrat">
                                Part Name <span class="text-harrier-red">*</span>
                            </label>
                            <input type="text" name="name" required
                                   minlength="2" maxlength="200"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-harrier-red focus:border-harrier-red bg-white shadow-sm transition-all duration-200 font-raleway"
                                   placeholder="Enter part name"
                                   data-validation="required|min:2|max:200">
                            <div class="field-error hidden text-sm text-red-600 font-raleway mt-1"></div>
                        </div>
                        
                        <!-- Part Number -->
                        <div class="space-y-2">
                            <label class="block text-sm font-bold text-harrier-dark font-montserrat">
                                Part Number
                            </label>
                            <input type="text" name="part_number"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-harrier-red focus:border-harrier-red bg-white shadow-sm transition-all duration-200 font-raleway"
                                   placeholder="Enter part number">
                        </div>
                        
                        <!-- SKU -->
                        <div class="space-y-2">
                            <label class="block text-sm font-bold text-harrier-dark font-montserrat">
                                SKU <span class="text-harrier-red">*</span>
                            </label>
                            <input type="text" name="sku" required
                                   minlength="2" maxlength="50"
                                   pattern="[A-Za-z0-9\-_]+"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-harrier-red focus:border-harrier-red bg-white shadow-sm transition-all duration-200 font-raleway"
                                   placeholder="Enter SKU (letters, numbers, hyphens, underscores only)"
                                   data-validation="required|min:2|max:50|alphanumeric">
                            <div class="field-error hidden text-sm text-red-600 font-raleway mt-1"></div>
                        </div>
                        
                        <!-- Barcode -->
                        <div class="space-y-2">
                            <label class="block text-sm font-bold text-harrier-dark font-montserrat">
                                Barcode
                            </label>
                            <input type="text" name="barcode"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-harrier-red focus:border-harrier-red bg-white shadow-sm transition-all duration-200 font-raleway"
                                   placeholder="Enter barcode">
                        </div>
                        
                        <!-- Category -->
                        <div class="space-y-2">
                            <label class="block text-sm font-bold text-harrier-dark font-montserrat">
                                Category <span class="text-harrier-red">*</span>
                            </label>
                            <select name="category_new" required
                                    class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-harrier-red focus:border-harrier-red bg-white shadow-sm transition-all duration-200 font-raleway"
                                    data-validation="required">
                                <option value="">Select Category</option>
                                {% for category in categories %}
                                    <option value="{{ category.id }}">{{ category.name }}</option>
                                {% endfor %}
                            </select>
                            <div class="field-error hidden text-sm text-red-600 font-raleway mt-1"></div>
                        </div>
                        
                        <!-- Supplier -->
                        <div class="space-y-2">
                            <label class="block text-sm font-bold text-harrier-dark font-montserrat">
                                Supplier
                            </label>
                            <select name="supplier"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-harrier-red focus:border-harrier-red bg-white shadow-sm transition-all duration-200 font-raleway">
                                <option value="">Select Supplier (Optional)</option>
                                {% for supplier in suppliers %}
                                    <option value="{{ supplier.id }}">{{ supplier.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                </div>
                
                <!-- Pricing & Inventory Section -->
                <div class="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
                    <h4 class="text-lg font-bold text-harrier-dark mb-4 font-montserrat flex items-center">
                        <i class="fas fa-dollar-sign mr-2 text-green-600"></i>Pricing & Inventory
                    </h4>
                    
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Price -->
                        <div class="space-y-2">
                            <label class="block text-sm font-bold text-harrier-dark font-montserrat">
                                Selling Price (KSh) <span class="text-harrier-red">*</span>
                            </label>
                            <input type="number" name="price" step="0.01" min="0.01" max="10000000" required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-harrier-red focus:border-harrier-red bg-white shadow-sm transition-all duration-200 font-raleway"
                                   placeholder="0.00"
                                   data-validation="required|min:0.01|max:10000000">
                            <div class="field-error hidden text-sm text-red-600 font-raleway mt-1"></div>
                        </div>
                        
                        <!-- Cost Price -->
                        <div class="space-y-2">
                            <label class="block text-sm font-bold text-harrier-dark font-montserrat">
                                Cost Price (KSh)
                            </label>
                            <input type="number" name="cost_price" step="0.01" min="0"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-harrier-red focus:border-harrier-red bg-white shadow-sm transition-all duration-200 font-raleway"
                                   placeholder="0.00">
                        </div>
                        
                        <!-- Discount Price -->
                        <div class="space-y-2">
                            <label class="block text-sm font-bold text-harrier-dark font-montserrat">
                                Discount Price (KSh)
                            </label>
                            <input type="number" name="discount_price" step="0.01" min="0"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-harrier-red focus:border-harrier-red bg-white shadow-sm transition-all duration-200 font-raleway"
                                   placeholder="0.00">
                        </div>
                        
                        <!-- Stock Quantity -->
                        <div class="space-y-2">
                            <label class="block text-sm font-bold text-harrier-dark font-montserrat">
                                Stock Quantity <span class="text-harrier-red">*</span>
                            </label>
                            <input type="number" name="stock_quantity" min="0" max="1000000" required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-harrier-red focus:border-harrier-red bg-white shadow-sm transition-all duration-200 font-raleway"
                                   placeholder="0"
                                   data-validation="required|min:0|max:1000000">
                            <div class="field-error hidden text-sm text-red-600 font-raleway mt-1"></div>
                        </div>
                        
                        <!-- Minimum Stock -->
                        <div class="space-y-2">
                            <label class="block text-sm font-bold text-harrier-dark font-montserrat">
                                Minimum Stock
                            </label>
                            <input type="number" name="minimum_stock" min="0"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-harrier-red focus:border-harrier-red bg-white shadow-sm transition-all duration-200 font-raleway"
                                   placeholder="0">
                        </div>
                        
                        <!-- Maximum Stock -->
                        <div class="space-y-2">
                            <label class="block text-sm font-bold text-harrier-dark font-montserrat">
                                Maximum Stock
                            </label>
                            <input type="number" name="maximum_stock" min="0"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-harrier-red focus:border-harrier-red bg-white shadow-sm transition-all duration-200 font-raleway"
                                   placeholder="0">
                        </div>
                    </div>
                </div>
                
                <!-- Description & Details Section -->
                <div class="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
                    <h4 class="text-lg font-bold text-harrier-dark mb-4 font-montserrat flex items-center">
                        <i class="fas fa-file-alt mr-2 text-blue-600"></i>Description & Details
                    </h4>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Description -->
                        <div class="space-y-2">
                            <label class="block text-sm font-bold text-harrier-dark font-montserrat">
                                Description
                            </label>
                            <textarea name="description" rows="4"
                                      class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-harrier-red focus:border-harrier-red bg-white shadow-sm transition-all duration-200 font-raleway resize-none"
                                      placeholder="Enter part description"></textarea>
                        </div>
                        
                        <!-- Specifications -->
                        <div class="space-y-2">
                            <label class="block text-sm font-bold text-harrier-dark font-montserrat">
                                Specifications
                            </label>
                            <textarea name="specifications" rows="4"
                                      class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-harrier-red focus:border-harrier-red bg-white shadow-sm transition-all duration-200 font-raleway resize-none"
                                      placeholder="Enter technical specifications"></textarea>
                        </div>
                        
                        <!-- Condition -->
                        <div class="space-y-2">
                            <label class="block text-sm font-bold text-harrier-dark font-montserrat">
                                Condition <span class="text-harrier-red">*</span>
                            </label>
                            <select name="condition" required
                                    class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-harrier-red focus:border-harrier-red bg-white shadow-sm transition-all duration-200 font-raleway"
                                    data-validation="required">
                                <option value="">Select Condition</option>
                                <option value="new">New</option>
                                <option value="used">Used</option>
                                <option value="refurbished">Refurbished</option>
                            </select>
                            <div class="field-error hidden text-sm text-red-600 font-raleway mt-1"></div>
                        </div>
                        
                        <!-- Unit -->
                        <div class="space-y-2">
                            <label class="block text-sm font-bold text-harrier-dark font-montserrat">
                                Unit
                            </label>
                            <select name="unit"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-harrier-red focus:border-harrier-red bg-white shadow-sm transition-all duration-200 font-raleway">
                                <option value="piece">Piece</option>
                                <option value="set">Set</option>
                                <option value="pair">Pair</option>
                                <option value="kit">Kit</option>
                                <option value="liter">Liter</option>
                                <option value="meter">Meter</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <!-- Image Upload Section -->
                <div class="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
                    <h4 class="text-lg font-bold text-harrier-dark mb-4 font-montserrat flex items-center">
                        <i class="fas fa-image mr-2 text-purple-600"></i>Product Image
                    </h4>
                    
                    <div class="space-y-2">
                        <label class="block text-sm font-bold text-harrier-dark font-montserrat">
                            Main Image
                        </label>
                        <input type="file" name="main_image" accept="image/*"
                               class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-harrier-red focus:border-harrier-red bg-white shadow-sm transition-all duration-200 font-raleway">
                        <p class="text-sm text-gray-500 font-raleway">Upload a high-quality image of the spare part (JPG, PNG, max 5MB)</p>
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="flex flex-col sm:flex-row items-center justify-end space-y-3 sm:space-y-0 sm:space-x-4 pt-6 border-t border-gray-200">
                    <button type="button"
                            class="w-full sm:w-auto px-6 py-3 bg-gray-100 text-gray-700 rounded-xl font-bold hover:bg-gray-200 transition-all duration-200 font-montserrat"
                            @click="show = false; setTimeout(() => $el.closest('.fixed').remove(), 200)">
                        <i class="fas fa-times mr-2"></i>Cancel
                    </button>
                    <button type="submit"
                            class="w-full sm:w-auto px-8 py-3 bg-gradient-to-r from-harrier-red to-harrier-red-dark text-white rounded-xl font-bold hover:from-harrier-red-dark hover:to-harrier-red transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl font-montserrat"
                            :disabled="isSubmitting"
                            :class="{ 'opacity-50 cursor-not-allowed': isSubmitting }">
                        <span x-show="!isSubmitting" class="flex items-center">
                            <i class="fas fa-plus mr-2"></i>Add Spare Part
                        </span>
                        <span x-show="isSubmitting" class="flex items-center">
                            <i class="fas fa-spinner fa-spin mr-2"></i>Adding...
                        </span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Form validation and error handling for Add Spare Part Modal
function handleFormSubmit() {
    console.log('handleFormSubmit called');

    // Clear previous errors
    clearFormErrors();

    // Show loading state
    showFormLoading(true);

    // For now, let's skip client-side validation to test the flow
    // We'll re-enable it once we confirm the basic submission works
    console.log('Form submission allowed');
    return true;
}

function handleFormResponse(event) {
    console.log('handleFormResponse called', event);
    const xhr = event.detail.xhr;
    const response = event.detail.xhr.response;

    console.log('XHR status:', xhr.status);
    console.log('Response:', response);

    // Hide loading state
    showFormLoading(false);

    try {
        // Try to parse JSON response
        const data = JSON.parse(response);
        console.log('Parsed data:', data);

        if (data.success) {
            // Handle success
            showFormSuccess(data.message || 'Spare part added successfully!');

            // Refresh the spare parts list
            const dynamicContent = document.getElementById('dynamic-content');
            if (dynamicContent) {
                // Trigger a refresh of the spare parts list
                htmx.ajax('GET', '{% url "core:vendor_spare_parts_search_htmx" %}', {
                    target: '#dynamic-content',
                    swap: 'innerHTML'
                });
            }

            // Close modal after 2 seconds
            setTimeout(() => {
                const modal = document.querySelector('.fixed');
                if (modal) {
                    modal.remove();
                }

                // Refresh the parts list
                if (typeof refreshPartsList === 'function') {
                    refreshPartsList();
                }
            }, 2000);

        } else if (data.errors) {
            // Handle validation errors
            showFormErrors(data.errors);
        } else {
            // Handle general error
            showFormErrors({ '__all__': [data.message || 'An error occurred while adding the spare part.'] });
        }
    } catch (e) {
        // Handle non-JSON response (likely HTML error page)
        if (xhr.status >= 400) {
            showFormErrors({ '__all__': [`Server error (${xhr.status}): Please try again later.`] });
        } else {
            // Assume success if we can't parse the response but status is OK
            showFormSuccess('Spare part added successfully!');
            setTimeout(() => {
                const modal = document.querySelector('.fixed');
                if (modal) modal.remove();
                if (typeof refreshPartsList === 'function') refreshPartsList();
            }, 1500);
        }
    }
}

function validateForm(form) {
    let isValid = true;
    const fields = form.querySelectorAll('[data-validation]');

    fields.forEach(field => {
        const validation = field.getAttribute('data-validation');
        const rules = validation.split('|');
        const value = field.value.trim();
        const fieldName = field.name;

        let fieldErrors = [];

        // Check each validation rule
        rules.forEach(rule => {
            if (rule === 'required' && !value) {
                fieldErrors.push('This field is required.');
            } else if (rule.startsWith('min:')) {
                const min = parseFloat(rule.split(':')[1]);
                if (field.type === 'number' && parseFloat(value) < min) {
                    fieldErrors.push(`Value must be at least ${min}.`);
                } else if (field.type === 'text' && value.length < min) {
                    fieldErrors.push(`Must be at least ${min} characters long.`);
                }
            } else if (rule.startsWith('max:')) {
                const max = parseFloat(rule.split(':')[1]);
                if (field.type === 'number' && parseFloat(value) > max) {
                    fieldErrors.push(`Value must not exceed ${max}.`);
                } else if (field.type === 'text' && value.length > max) {
                    fieldErrors.push(`Must not exceed ${max} characters.`);
                }
            } else if (rule === 'alphanumeric' && value && !/^[A-Za-z0-9\-_]+$/.test(value)) {
                fieldErrors.push('Only letters, numbers, hyphens, and underscores are allowed.');
            }
        });

        // Show field-specific errors
        if (fieldErrors.length > 0) {
            showFieldError(field, fieldErrors[0]);
            isValid = false;
        } else {
            clearFieldError(field);
        }
    });

    return isValid;
}

function showFieldError(field, message) {
    const errorDiv = field.parentNode.querySelector('.field-error');
    if (errorDiv) {
        errorDiv.textContent = message;
        errorDiv.classList.remove('hidden');
    }

    // Add error styling to field
    field.classList.add('border-red-500', 'focus:ring-red-500', 'focus:border-red-500');
    field.classList.remove('border-gray-300', 'focus:ring-harrier-red', 'focus:border-harrier-red');
}

function clearFieldError(field) {
    const errorDiv = field.parentNode.querySelector('.field-error');
    if (errorDiv) {
        errorDiv.classList.add('hidden');
    }

    // Remove error styling from field
    field.classList.remove('border-red-500', 'focus:ring-red-500', 'focus:border-red-500');
    field.classList.add('border-gray-300', 'focus:ring-harrier-red', 'focus:border-harrier-red');
}

function clearFormErrors() {
    // Hide main error container
    const errorContainer = document.getElementById('form-errors');
    if (errorContainer) {
        errorContainer.classList.add('hidden');
    }

    // Hide success container
    const successContainer = document.getElementById('form-success');
    if (successContainer) {
        successContainer.classList.add('hidden');
    }

    // Clear all field errors
    const form = document.getElementById('add-spare-part-form');
    const fields = form.querySelectorAll('[data-validation]');
    fields.forEach(field => clearFieldError(field));
}

function showFormErrors(errors) {
    const errorContainer = document.getElementById('form-errors');
    const errorList = document.getElementById('error-list');

    if (!errorContainer || !errorList) return;

    // Clear previous errors
    errorList.innerHTML = '';

    // Add new errors
    const errorMessages = [];

    Object.keys(errors).forEach(field => {
        const fieldErrors = Array.isArray(errors[field]) ? errors[field] : [errors[field]];
        fieldErrors.forEach(error => {
            if (field === '__all__') {
                errorMessages.push(error);
            } else {
                // Try to find the field and show inline error
                const fieldElement = document.querySelector(`[name="${field}"]`);
                if (fieldElement) {
                    showFieldError(fieldElement, error);
                } else {
                    errorMessages.push(`${field}: ${error}`);
                }
            }
        });
    });

    // Show general errors
    if (errorMessages.length > 0) {
        errorMessages.forEach(message => {
            const li = document.createElement('div');
            li.textContent = `• ${message}`;
            li.className = 'mb-1';
            errorList.appendChild(li);
        });

        errorContainer.classList.remove('hidden');

        // Scroll to top of modal to show errors
        const modalBody = document.querySelector('.modal-body');
        if (modalBody) {
            modalBody.scrollTop = 0;
        }
    }
}

function showFormSuccess(message) {
    const successContainer = document.getElementById('form-success');
    const successMessage = document.getElementById('success-message');

    if (successContainer && successMessage) {
        successMessage.textContent = message;
        successContainer.classList.remove('hidden');

        // Scroll to top to show success message
        const modalBody = document.querySelector('.modal-body');
        if (modalBody) {
            modalBody.scrollTop = 0;
        }
    }
}

function showFormLoading(show) {
    const submitButton = document.querySelector('#add-spare-part-form button[type="submit"]');
    if (submitButton) {
        if (show) {
            submitButton.disabled = true;
            submitButton.classList.add('opacity-50', 'cursor-not-allowed');
        } else {
            submitButton.disabled = false;
            submitButton.classList.remove('opacity-50', 'cursor-not-allowed');
        }
    }
}

// Add real-time validation on input
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('add-spare-part-form');
    if (form) {
        const fields = form.querySelectorAll('[data-validation]');
        fields.forEach(field => {
            field.addEventListener('blur', function() {
                // Validate single field on blur
                const validation = this.getAttribute('data-validation');
                if (validation) {
                    validateForm(form);
                }
            });

            field.addEventListener('input', function() {
                // Clear error on input
                clearFieldError(this);
            });
        });
    }
});
</script>
